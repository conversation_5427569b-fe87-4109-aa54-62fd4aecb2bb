/**
 * 环境检测和配置模块
 * 负责检测运行环境并提供相应的配置
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0 (2025-01-28): 初始版本，提供环境检测和开发环境优化功能
 */

class Environment {
    constructor() {
        this.isLocal = this.detectLocalEnvironment();
        this.isDevelopment = this.detectDevelopmentEnvironment();
        this.isProduction = !this.isLocal && !this.isDevelopment;
        this.initialized = false;
    }

    /**
     * 检测是否为本地开发环境
     * @returns {boolean} 如果是本地环境返回true，否则返回false
     */
    detectLocalEnvironment() {
        const location = window.location;
        const hostname = location.hostname;
        const protocol = location.protocol;
        const port = location.port;

        // 检测本地主机名
        const localHostnames = ['localhost', '127.0.0.1', '0.0.0.0'];
        if (localHostnames.includes(hostname)) {
            return true;
        }

        // 检测file协议
        if (protocol === 'file:') {
            return true;
        }

        // 检测常见开发端口
        const developmentPorts = ['3000', '8000', '8080', '5000', '4000', '9000', '3001', '8001'];
        if (port && developmentPorts.includes(port)) {
            return true;
        }

        // 检测本地IP地址范围
        if (hostname.startsWith('192.168.') || hostname.startsWith('10.') || hostname.startsWith('172.')) {
            return true;
        }

        return false;
    }

    /**
     * 检测是否为开发环境（非本地但仍在开发中）
     * @returns {boolean} 如果是开发环境返回true，否则返回false
     */
    detectDevelopmentEnvironment() {
        const hostname = window.location.hostname;
        
        // 检测开发域名模式
        const devPatterns = [
            /^dev\./,
            /^test\./,
            /^staging\./,
            /\.dev$/,
            /\.test$/,
            /\.local$/
        ];

        return devPatterns.some(pattern => pattern.test(hostname));
    }

    /**
     * 初始化环境配置
     */
    init() {
        if (this.initialized) return;

        this.logEnvironmentInfo();
        this.setupDevelopmentOptimizations();
        this.initialized = true;
    }

    /**
     * 输出环境信息
     */
    logEnvironmentInfo() {
        const location = window.location;
        const envType = this.isLocal ? '本地环境' : 
                       this.isDevelopment ? '开发环境' : '生产环境';
        
        console.info(
            `%c[Environment] 当前运行环境: ${envType}`,
            'color: #10B981; font-weight: bold;',
            `\n域名: ${location.hostname}`,
            `\n协议: ${location.protocol}`,
            `\n端口: ${location.port || '默认'}`,
            `\n完整URL: ${location.href}`
        );
    }

    /**
     * 设置开发环境优化
     */
    setupDevelopmentOptimizations() {
        if (this.isLocal || this.isDevelopment) {
            // 禁用Tailwind CSS CDN警告（仅在开发环境）
            this.suppressTailwindWarnings();
            
            // 添加开发环境标识
            this.addDevelopmentIndicator();
        }
    }

    /**
     * 抑制Tailwind CSS CDN警告
     */
    suppressTailwindWarnings() {
        // 立即重写console.warn以过滤Tailwind CDN警告
        const originalWarn = console.warn;
        console.warn = function(...args) {
            const message = args.join(' ');
            if (message.includes('cdn.tailwindcss.com should not be used in production')) {
                // 在开发环境下将警告转为信息提示
                console.info(
                    '%c[Environment] Tailwind CSS CDN 仅用于开发环境',
                    'color: #F59E0B; font-weight: bold;',
                    '\n生产环境请使用本地构建版本'
                );
                return;
            }
            originalWarn.apply(console, args);
        };

        // 同时处理可能已经存在的警告
        setTimeout(() => {
            console.info(
                '%c[Environment] Tailwind CSS CDN 警告已被抑制',
                'color: #10B981; font-weight: bold;',
                '\n如果仍看到警告，请刷新页面'
            );
        }, 100);
    }

    /**
     * 添加开发环境标识
     */
    addDevelopmentIndicator() {
        if (this.isLocal) {
            // 在页面标题后添加开发环境标识
            document.title += ' [开发环境]';
            
            // 在控制台显示开发提示
            console.info(
                '%c[Environment] 开发环境功能已启用',
                'color: #3B82F6; font-weight: bold;',
                '\n- Tailwind CSS CDN 警告已抑制',
                '\n- 访问统计功能已禁用',
                '\n- 开发环境标识已添加'
            );
        }
    }

    /**
     * 获取当前环境信息
     * @returns {Object} 包含环境信息的对象
     */
    getEnvironmentInfo() {
        const location = window.location;
        return {
            isLocal: this.isLocal,
            isDevelopment: this.isDevelopment,
            isProduction: this.isProduction,
            hostname: location.hostname,
            protocol: location.protocol,
            port: location.port,
            url: location.href,
            userAgent: navigator.userAgent
        };
    }

    /**
     * 检查是否应该加载某个功能
     * @param {string} feature 功能名称
     * @returns {boolean} 是否应该加载
     */
    shouldLoadFeature(feature) {
        const config = {
            'analytics': !this.isLocal, // 统计功能仅在非本地环境加载
            'busuanzi': !this.isLocal,  // 不蒜子统计仅在非本地环境加载
            'sentry': this.isProduction, // 错误监控仅在生产环境加载
            'hotjar': this.isProduction, // 用户行为分析仅在生产环境加载
            'gtag': this.isProduction    // Google Analytics仅在生产环境加载
        };

        return config[feature] !== undefined ? config[feature] : true;
    }
}

// 创建全局环境实例
window.Environment = Environment;

// 立即初始化环境检测
const environment = new Environment();
environment.init();

// 导出环境实例
window.AppEnvironment = environment;
