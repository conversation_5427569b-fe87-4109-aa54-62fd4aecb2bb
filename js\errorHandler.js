/**
 * 错误处理模块
 * 负责捕获和处理页面中的各种错误，提升用户体验
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0 (2025-01-28): 初始版本，提供全局错误处理和资源加载错误处理
 */

class ErrorHandler {
    constructor() {
        this.initialized = false;
        this.errorCount = 0;
        this.maxErrors = 10; // 最大错误数量限制
        this.isLocalEnvironment = window.AppEnvironment?.isLocal ?? false;
    }

    /**
     * 初始化错误处理
     */
    init() {
        if (this.initialized) return;

        this.setupGlobalErrorHandling();
        this.setupResourceErrorHandling();
        this.setupUnhandledRejectionHandling();
        this.initialized = true;

        if (this.isLocalEnvironment) {
            console.info(
                '%c[ErrorHandler] 错误处理模块已启用',
                'color: #EF4444; font-weight: bold;',
                '\n- 全局错误捕获已启用',
                '\n- 资源加载错误处理已启用',
                '\n- Promise拒绝处理已启用'
            );
        }
    }

    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'JavaScript错误',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                timestamp: new Date().toISOString()
            });
        });
    }

    /**
     * 设置资源加载错误处理
     */
    setupResourceErrorHandling() {
        window.addEventListener('error', (event) => {
            // 检查是否为资源加载错误
            if (event.target !== window) {
                const element = event.target;
                const resourceType = element.tagName.toLowerCase();
                const resourceUrl = element.src || element.href;

                // 过滤掉已知的外部资源错误
                if (this.shouldIgnoreResourceError(resourceUrl)) {
                    return;
                }

                this.handleResourceError({
                    type: '资源加载错误',
                    resourceType: resourceType,
                    resourceUrl: resourceUrl,
                    timestamp: new Date().toISOString()
                });
            }
        }, true); // 使用捕获阶段
    }

    /**
     * 设置未处理的Promise拒绝处理
     */
    setupUnhandledRejectionHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'Promise拒绝',
                message: event.reason?.message || event.reason,
                promise: event.promise,
                timestamp: new Date().toISOString()
            });
        });
    }

    /**
     * 处理一般错误
     * @param {Object} errorInfo 错误信息
     */
    handleError(errorInfo) {
        this.errorCount++;

        // 防止错误过多导致性能问题
        if (this.errorCount > this.maxErrors) {
            return;
        }

        // 在开发环境下显示详细错误信息
        if (this.isLocalEnvironment) {
            console.group(`%c[ErrorHandler] ${errorInfo.type}`, 'color: #EF4444; font-weight: bold;');
            console.error('错误详情:', errorInfo);
            console.groupEnd();
        } else {
            // 生产环境下只记录简要信息
            console.warn(`[ErrorHandler] ${errorInfo.type}: ${errorInfo.message}`);
        }

        // 这里可以添加错误上报逻辑
        // this.reportError(errorInfo);
    }

    /**
     * 处理资源加载错误
     * @param {Object} errorInfo 错误信息
     */
    handleResourceError(errorInfo) {
        // 在开发环境下显示资源错误信息
        if (this.isLocalEnvironment) {
            console.warn(
                `%c[ErrorHandler] ${errorInfo.resourceType.toUpperCase()} 资源加载失败`,
                'color: #F59E0B; font-weight: bold;',
                `\nURL: ${errorInfo.resourceUrl}`,
                `\n时间: ${errorInfo.timestamp}`
            );
        }

        // 尝试资源回退策略
        this.attemptResourceFallback(errorInfo);
    }

    /**
     * 判断是否应该忽略某个资源错误
     * @param {string} resourceUrl 资源URL
     * @returns {boolean} 是否应该忽略
     */
    shouldIgnoreResourceError(resourceUrl) {
        if (!resourceUrl) return true;

        // 忽略的URL模式
        const ignorePatterns = [
            /busuanzi\.ibruce\.info/, // 不蒜子统计脚本
            /google-analytics\.com/, // Google Analytics
            /googletagmanager\.com/, // Google Tag Manager
            /facebook\.net/, // Facebook像素
            /doubleclick\.net/, // Google广告
            /googlesyndication\.com/, // Google广告
            /chrome-extension:\/\//, // 浏览器扩展
            /moz-extension:\/\//, // Firefox扩展
            /safari-extension:\/\//, // Safari扩展
            /^data:/, // Data URLs
            /^blob:/ // Blob URLs
        ];

        return ignorePatterns.some(pattern => pattern.test(resourceUrl));
    }

    /**
     * 尝试资源回退策略
     * @param {Object} errorInfo 错误信息
     */
    attemptResourceFallback(errorInfo) {
        const { resourceType, resourceUrl } = errorInfo;

        // 针对不同资源类型的回退策略
        switch (resourceType) {
            case 'script':
                this.handleScriptFallback(resourceUrl);
                break;
            case 'link':
                this.handleStyleFallback(resourceUrl);
                break;
            case 'img':
                this.handleImageFallback(resourceUrl);
                break;
            default:
                // 其他资源类型暂不处理
                break;
        }
    }

    /**
     * 处理脚本回退
     * @param {string} scriptUrl 脚本URL
     */
    handleScriptFallback(scriptUrl) {
        // 这里可以添加脚本回退逻辑
        // 例如：加载本地备份脚本
        if (this.isLocalEnvironment) {
            console.info(`[ErrorHandler] 脚本回退策略可在此处实现: ${scriptUrl}`);
        }
    }

    /**
     * 处理样式回退
     * @param {string} styleUrl 样式URL
     */
    handleStyleFallback(styleUrl) {
        // 这里可以添加样式回退逻辑
        if (this.isLocalEnvironment) {
            console.info(`[ErrorHandler] 样式回退策略可在此处实现: ${styleUrl}`);
        }
    }

    /**
     * 处理图片回退
     * @param {string} imageUrl 图片URL
     */
    handleImageFallback(imageUrl) {
        // 这里可以添加图片回退逻辑
        // 例如：显示默认图片
        if (this.isLocalEnvironment) {
            console.info(`[ErrorHandler] 图片回退策略可在此处实现: ${imageUrl}`);
        }
    }

    /**
     * 获取错误统计信息
     * @returns {Object} 错误统计
     */
    getErrorStats() {
        return {
            errorCount: this.errorCount,
            maxErrors: this.maxErrors,
            initialized: this.initialized
        };
    }

    /**
     * 重置错误计数
     */
    resetErrorCount() {
        this.errorCount = 0;
    }

    /**
     * 销毁错误处理器
     */
    destroy() {
        // 移除事件监听器
        // 注意：实际上很难完全移除，因为我们没有保存监听器引用
        this.initialized = false;
        this.errorCount = 0;
    }
}

// 创建全局错误处理实例
window.ErrorHandler = ErrorHandler;

// 立即初始化错误处理
const errorHandler = new ErrorHandler();
errorHandler.init();

// 导出错误处理实例
window.AppErrorHandler = errorHandler;
